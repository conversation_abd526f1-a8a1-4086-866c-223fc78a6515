from __future__ import annotations
import asyncio
import tempfile
import os
from typing import As<PERSON><PERSON><PERSON><PERSON>, Dict, Optional, List
from fastapi import FastAP<PERSON>, HTTPException, Query, Path, Header, Depends, UploadFile, File, Form # type: ignore
from fastapi.responses import StreamingResponse
from optimed.core.domain import PatientContext, User, JournalEntry, ProcessingResult, JournalType
from optimed.apps.orchestrator.factory import create_app_dependencies

from pydantic import BaseModel

_engine, _fhir, _vector_store, _user_repo = create_app_dependencies()

app = FastAPI(title="OptiMed API Gateway", version="0.1.0", debug=True)

class ChatRequest(BaseModel):
    patient_id: str | None = None
    user_prompt: str | None = None
    chat_mode: bool = False

    user_id: str | None = None  # Optional user ID for tracking
    session_id: str | None = None  # Optional session ID for tracking

class PatientOut(BaseModel):
    id: str
    name: str
    age: int
    sex: str
    care_unit: str
    vitals: Dict[str, str]
    labs:   Dict[str, str]

class UserOut(BaseModel):
    user_id: str 
    username: str
    email: str
    first_name: str | None
    last_name: str | None
    role: str
    status: str
    department: str | None
    full_name: str
    display_name: str

class LoginRequest(BaseModel):
    username: str
    password: str

class LoginResponse(BaseModel):
    user: UserOut
    session_id: str
    message: str



async def get_current_user(
    x_session_id: str = Header(None, alias="X-Session-ID")
) -> Optional[User]:
    
    if not x_session_id:
        return None
    
    try:
        session = await _user_repo.get_active_session(x_session_id)
        if not session:
            return None
        
        user = await _user_repo.get_user_by_id(session.user_id)
        if user:
            await _user_repo.update_session_activity(x_session_id)
        
        return user
    except Exception as e:
        print(f"Error fetching current user: {e}")
        return None
    

async def get_user_context(user: Optional[User] = Depends(get_current_user)) -> dict:
    """Dependency to fetch user context."""
    return {
        "user_id": user.user_id if user else None,
        "user": user
    }

@app.post("/health", summary="Health Check")
async def health():
    """Simple health check endpoint."""
    return {"status": "ok"}

@app.post("/diagnose", response_model=dict)
async def diagnose(req: ChatRequest, user_ctx: dict = Depends(get_user_context)):

    user_id = user_ctx.get("user_id") or req.user_id
    current_user = user_ctx.get("user")

    if current_user:
        print(f"[API] Diagnosis request from {current_user.display_name} ({current_user.role.value})")

    try:
        # Fetch patient context
        patient_ctx: PatientContext = await _fhir.get_patient(str(req.patient_id)) # Cast to str to avoid type errors
    except Exception as e:
        raise HTTPException(status_code=404, detail=f"Patient not found: {e}")

    result = await _engine.run(patient_ctx, req.user_prompt, chat_mode=req.chat_mode, user_id=user_id, session_id=req.session_id)
    if hasattr(result, 'model_dump'):
        return result.model_dump()
    else:
        return result


@app.post("/api/chat-history")
async def get_chat_history(
    request: dict,
    user_ctx: dict = Depends(get_user_context)
):
    """Get chat history for a patient/session."""
    patient_id = request.get("patient_id")
    session_id = request.get("session_id")
    mode = request.get("mode", "diagnose")
    
    print(f"[API] Loading chat history for patient_id={patient_id}, session_id={session_id}, mode={mode}")
    
    try:
        if patient_id:
            # Get patient-specific history with session filter
            history_data = await _engine.vector_store.get_chat_history(
                patient_id=str(patient_id), 
                session_id=session_id,
                limit=50
            )
        else:
            # Get general chat history
            history_data = await _engine.vector_store.get_chat_history(
                patient_id="general_chat", 
                session_id=session_id,
                limit=50
            )
        
        print(f"[API] Returning {len(history_data)} chat history messages")
        return {"messages": history_data}
        
    except Exception as e:
        print(f"[API] Error loading chat history: {e}")
        return {"messages": []}

@app.get("/patients", response_model=list[PatientOut], summary="Search patients")
async def search_patients(q: str = Query("", alias="q")):
    try:
        # Adapt to your repository signature
        domain_objs = await _fhir.search_patients(q)  
        return [
            PatientOut(
                id=p.patient_id,
                name=p.name,
                age=p.age,
                sex=p.sex,
                care_unit=p.care_unit,
                vitals=p.vitals,
                labs=p.labs,
            )
            for p in domain_objs
        ]
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Could not load patients: {e}")


@app.get(
    "/patients/{patient_id}",
    response_model=PatientOut,
    summary="Get single patient by ID",
)
async def get_patient(patient_id: str = Path(..., description="The patient ID")):
    try:
        pctx = await _fhir.get_patient(patient_id)
    except Exception as e:
        raise HTTPException(status_code=404, detail=f"Patient not found: {e}")
    return PatientOut(
        id=pctx.patient_id,
        name=pctx.name,
        age=pctx.age,
        sex=pctx.sex,
        care_unit=pctx.care_unit,
        vitals=pctx.vitals,
        labs=pctx.labs,
    )


@app.delete("/patients/{patient_id}/chat-history", summary="Delete patient by ID")
async def clear_patient_chat_history(patient_id: str = Path(..., description="The patient ID")):
    try:

        await _vector_store.clear_chat_history(patient_id)
        return {"message": f"Chat history cleared for patient {patient_id}"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Could not clear chat history: {e}")

async def _event_stream(patient: PatientContext) -> AsyncGenerator[str, None]:
    async for delta in _engine.run_stream(patient):
        yield f"data: {delta}\n\n"
        await asyncio.sleep(0.1)  # Simulate async delay for streaming


@app.post("/diagnose/stream")
async def diagnose_stream(req: ChatRequest):
    try:
        patient = await _fhir.get_patient(str(req.patient_id)) # Cast to str to avoid type errors
    except Exception as e:
        raise HTTPException(status_code=404, detail=f"Patient not found: {e}")
    
    return StreamingResponse(_event_stream(patient), media_type="text/event-stream")


@app.post("/chat", response_model=dict)
async def chat(req: ChatRequest, user_ctx: dict = Depends(get_user_context)):
    """General chat endpoint that works with or without patient context."""
    user_id = user_ctx.get("user_id") or req.user_id
    current_user = user_ctx.get("user")

    if current_user:
        print(f"[API] Chat request from {current_user.display_name} ({current_user.role.value})")

    patient_ctx = None
    if req.patient_id is not None:
        try:
            patient_ctx = await _fhir.get_patient(req.patient_id)
        except Exception as e:
            # Log the error but continue with general chat
            print(f"Could not fetch patient {req.patient_id}: {e}")
    
    # Create a mock patient context for general chat
    if not patient_ctx:
        result = await _engine.run(user_prompt=req.user_prompt, chat_mode=req.chat_mode, user_id=user_id, session_id=req.session_id)
        return result
    else:
        result = await _engine.run(patient=patient_ctx, user_prompt=req.user_prompt, chat_mode=req.chat_mode, user_id=user_id, session_id=req.session_id)
        if hasattr(result, 'model_dump'):
            return result.model_dump()
        else:
            return result
        
@app.post("/auth/login", response_model=LoginResponse)
async def login(req: LoginRequest):
    """Authenticate user and create session."""
    # For now, simple username lookup (implement proper password checking later)
    user = await _user_repo.get_user_by_username(req.username)
    if not user:
        raise HTTPException(status_code=401, detail="Invalid credentials")
    
    # Create session
    import uuid
    from datetime import datetime
    from optimed.core.domain import UserSession
    
    session = UserSession(
        session_id=str(uuid.uuid4()),
        user_id=user.user_id,
        login_time=datetime.now(),
        last_activity=datetime.now(),
        is_active=True
    )
    
    await _user_repo.create_session(session)
    
    return LoginResponse(
        user=UserOut(
            user_id=user.user_id,
            username=user.username,
            email=user.email,
            first_name=user.first_name,
            last_name=user.last_name,
            role=user.role.value,
            status=user.status.value,
            department=user.department,
            full_name=user.full_name,
            display_name=user.display_name
        ),
        session_id=session.session_id,
        message="Login successful"
    )

@app.get("/users/by-role/{role}", response_model=list[UserOut])
async def get_users_by_role(role: str, user_ctx: dict = Depends(get_user_context)):
    """Get users by role (admin only)."""
    current_user = user_ctx.get("user")
    if not current_user or current_user.role.value not in ["admin", "doctor"]:
        raise HTTPException(status_code=403, detail="Insufficient permissions")
    
    users = await _user_repo.list_users_by_role(role)
    return [
        UserOut(
            user_id=u.user_id,
            username=u.username,
            email=u.email,
            first_name=u.first_name,
            last_name=u.last_name,
            role=u.role.value,
            status=u.status.value,
            department=u.department,
            full_name=u.full_name,
            display_name=u.display_name
        )
        for u in users
    ]


# Journal Processing Models
# =========================

class JournalUploadRequest(BaseModel):
    patient_id: Optional[str] = None
    title: Optional[str] = None
    journal_type: Optional[str] = None

class JournalOut(BaseModel):
    journal_id: str
    patient_id: Optional[str]
    user_id: Optional[str]
    title: str
    journal_type: str
    processing_status: str
    uploaded_at: str
    processed_at: Optional[str]
    word_count: Optional[int]
    has_interpretation: bool

class ProcessingResultOut(BaseModel):
    result_id: str
    success: bool
    processing_time_seconds: Optional[float]
    error_message: Optional[str]
    warnings: List[str]
    journal: Optional[JournalOut]

class JournalSearchRequest(BaseModel):
    query: str
    journal_type: Optional[str] = None
    patient_id: Optional[str] = None
    limit: int = 10


# Journal Processing Endpoints
# ============================

@app.post("/journals/upload", response_model=ProcessingResultOut)
async def upload_journal(
    file: UploadFile = File(...),
    patient_id: Optional[str] = Form(None),
    title: Optional[str] = Form(None),
    journal_type: Optional[str] = Form(None),
    user_ctx: dict = Depends(get_user_context)
):
    """Upload and process a journal document."""

    # Get current user
    current_user = user_ctx.get("user")
    user_id = user_ctx.get("user_id")
    session_id = user_ctx.get("session_id")

    if not current_user:
        raise HTTPException(status_code=401, detail="Authentication required")

    # Validate file
    if not file.filename:
        raise HTTPException(status_code=400, detail="No file provided")

    # Check file size (limit to 50MB)
    max_size = 50 * 1024 * 1024  # 50MB
    file_content = await file.read()
    if len(file_content) > max_size:
        raise HTTPException(status_code=413, detail="File too large (max 50MB)")

    try:
        # Create journal processor (this would be injected in real implementation)
        from optimed.adapters.document_parsers import TextParser, PDFParser, DOCXParser, JSONParser, FormatDetectorImpl
        from optimed.adapters.journal_interpreter import ClaudeJournalInterpreter
        from optimed.adapters.journal_repository import PGJournalRepository
        from optimed.adapters.journal_processor import LangGraphJournalProcessor
        from optimed.adapters.anthropic_claude.client import AnthropicClaudeClient

        # Initialize components (in production, these would be dependency injected)
        format_detector = FormatDetectorImpl()
        text_parser = TextParser()  # Could be a composite parser
        llm_client = AnthropicClaudeClient()
        journal_interpreter = ClaudeJournalInterpreter(llm_client)
        journal_repository = PGJournalRepository(os.getenv("PGVECTOR_DSN"))

        processor = LangGraphJournalProcessor(
            document_parser=text_parser,
            format_detector=format_detector,
            journal_interpreter=journal_interpreter,
            journal_repository=journal_repository,
            vector_store=_vector_store
        )

        # Process the document
        result = await processor.process_document(
            file_path=file_content,
            filename=file.filename,
            patient_id=patient_id,
            user_id=user_id,
            session_id=session_id
        )

        # Convert to response format
        journal_out = None
        if result.journal_entry:
            journal_out = JournalOut(
                journal_id=result.journal_entry.journal_id,
                patient_id=result.journal_entry.patient_id,
                user_id=result.journal_entry.user_id,
                title=result.journal_entry.title,
                journal_type=result.journal_entry.journal_type.value,
                processing_status=result.journal_entry.processing_status.value,
                uploaded_at=result.journal_entry.uploaded_at.isoformat(),
                processed_at=result.journal_entry.processed_at.isoformat() if result.journal_entry.processed_at else None,
                word_count=result.journal_entry.metadata.word_count,
                has_interpretation=result.interpretation is not None
            )

        return ProcessingResultOut(
            result_id=result.result_id,
            success=result.success,
            processing_time_seconds=result.total_processing_time_seconds,
            error_message=result.error_message,
            warnings=result.warnings,
            journal=journal_out
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Processing failed: {str(e)}")


@app.get("/journals/{journal_id}", response_model=dict)
async def get_journal(
    journal_id: str,
    include_content: bool = Query(False, description="Include full content"),
    include_interpretation: bool = Query(True, description="Include AI interpretation"),
    user_ctx: dict = Depends(get_user_context)
):
    """Get a journal entry by ID."""

    current_user = user_ctx.get("user")
    if not current_user:
        raise HTTPException(status_code=401, detail="Authentication required")

    try:
        # Get journal repository (would be dependency injected)
        from optimed.adapters.journal_repository import PGJournalRepository
        journal_repo = PGJournalRepository(os.getenv("PGVECTOR_DSN"))

        journal = await journal_repo.get_journal(journal_id)
        if not journal:
            raise HTTPException(status_code=404, detail="Journal not found")

        # Check permissions (user can access their own journals or patient-linked journals)
        if journal.user_id != current_user.user_id and current_user.role.value not in ["admin", "doctor"]:
            raise HTTPException(status_code=403, detail="Access denied")

        result = {
            "journal_id": journal.journal_id,
            "patient_id": journal.patient_id,
            "user_id": journal.user_id,
            "title": journal.title,
            "journal_type": journal.journal_type.value,
            "processing_status": journal.processing_status.value,
            "uploaded_at": journal.uploaded_at.isoformat(),
            "processed_at": journal.processed_at.isoformat() if journal.processed_at else None,
            "metadata": journal.metadata.model_dump(),
        }

        if include_content:
            result["content"] = journal.content
            result["abstract"] = journal.abstract
            result["sections"] = journal.sections

        if include_interpretation:
            interpretation = await journal_repo.get_interpretation(journal_id)
            if interpretation:
                result["interpretation"] = {
                    "summary": interpretation.summary,
                    "key_findings": interpretation.key_findings,
                    "clinical_significance": interpretation.clinical_significance,
                    "overall_confidence": interpretation.overall_confidence,
                    "confidence_level": interpretation.confidence_level.value,
                    "diseases": interpretation.diseases,
                    "medications": interpretation.medications,
                    "procedures": interpretation.procedures,
                }

        return result

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to retrieve journal: {str(e)}")


@app.post("/journals/search", response_model=List[JournalOut])
async def search_journals(
    request: JournalSearchRequest,
    user_ctx: dict = Depends(get_user_context)
):
    """Search journals by content or metadata."""

    current_user = user_ctx.get("user")
    if not current_user:
        raise HTTPException(status_code=401, detail="Authentication required")

    try:
        from optimed.adapters.journal_repository import PGJournalRepository
        from optimed.core.domain import JournalType

        journal_repo = PGJournalRepository(os.getenv("PGVECTOR_DSN"))

        # Convert journal type string to enum
        journal_type_enum = None
        if request.journal_type:
            try:
                journal_type_enum = JournalType(request.journal_type)
            except ValueError:
                raise HTTPException(status_code=400, detail=f"Invalid journal type: {request.journal_type}")

        # Search journals
        journals = await journal_repo.search_journals(
            query=request.query,
            journal_type=journal_type_enum,
            patient_id=request.patient_id,
            limit=request.limit
        )

        # Filter results based on user permissions
        filtered_journals = []
        for journal in journals:
            # Users can see their own journals or patient-linked journals if they have access
            if (journal.user_id == current_user.user_id or
                current_user.role.value in ["admin", "doctor"]):
                filtered_journals.append(journal)

        # Convert to response format
        return [
            JournalOut(
                journal_id=j.journal_id,
                patient_id=j.patient_id,
                user_id=j.user_id,
                title=j.title,
                journal_type=j.journal_type.value,
                processing_status=j.processing_status.value,
                uploaded_at=j.uploaded_at.isoformat(),
                processed_at=j.processed_at.isoformat() if j.processed_at else None,
                word_count=j.metadata.word_count,
                has_interpretation=True  # Would check if interpretation exists
            )
            for j in filtered_journals
        ]

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Search failed: {str(e)}")


@app.get("/patients/{patient_id}/journals", response_model=List[JournalOut])
async def get_patient_journals(
    patient_id: str,
    user_ctx: dict = Depends(get_user_context)
):
    """Get all journals for a specific patient."""

    current_user = user_ctx.get("user")
    if not current_user:
        raise HTTPException(status_code=401, detail="Authentication required")

    # Check permissions
    if current_user.role.value not in ["admin", "doctor", "nurse"]:
        raise HTTPException(status_code=403, detail="Insufficient permissions")

    try:
        from optimed.adapters.journal_repository import PGJournalRepository
        journal_repo = PGJournalRepository(os.getenv("PGVECTOR_DSN"))

        journals = await journal_repo.get_patient_journals(patient_id)

        return [
            JournalOut(
                journal_id=j.journal_id,
                patient_id=j.patient_id,
                user_id=j.user_id,
                title=j.title,
                journal_type=j.journal_type.value,
                processing_status=j.processing_status.value,
                uploaded_at=j.uploaded_at.isoformat(),
                processed_at=j.processed_at.isoformat() if j.processed_at else None,
                word_count=j.metadata.word_count,
                has_interpretation=True  # Would check if interpretation exists
            )
            for j in journals
        ]

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to retrieve patient journals: {str(e)}")


@app.post("/journals/{journal_id}/reprocess", response_model=ProcessingResultOut)
async def reprocess_journal(
    journal_id: str,
    user_ctx: dict = Depends(get_user_context)
):
    """Reprocess a journal with updated AI models."""

    current_user = user_ctx.get("user")
    if not current_user:
        raise HTTPException(status_code=401, detail="Authentication required")

    # Only admins and doctors can reprocess
    if current_user.role.value not in ["admin", "doctor"]:
        raise HTTPException(status_code=403, detail="Insufficient permissions")

    try:
        # Create processor (would be dependency injected)
        from optimed.adapters.document_parsers import FormatDetectorImpl, TextParser
        from optimed.adapters.journal_interpreter import ClaudeJournalInterpreter
        from optimed.adapters.journal_repository import PGJournalRepository
        from optimed.adapters.journal_processor import LangGraphJournalProcessor
        from optimed.adapters.anthropic_claude.client import AnthropicClaudeClient

        format_detector = FormatDetectorImpl()
        text_parser = TextParser()
        llm_client = AnthropicClaudeClient()
        journal_interpreter = ClaudeJournalInterpreter(llm_client)
        journal_repository = PGJournalRepository(os.getenv("PGVECTOR_DSN"))

        processor = LangGraphJournalProcessor(
            document_parser=text_parser,
            format_detector=format_detector,
            journal_interpreter=journal_interpreter,
            journal_repository=journal_repository,
            vector_store=_vector_store
        )

        # Reprocess the journal
        result = await processor.reprocess_journal(journal_id)

        # Convert to response format
        journal_out = None
        if result.journal_entry:
            journal_out = JournalOut(
                journal_id=result.journal_entry.journal_id,
                patient_id=result.journal_entry.patient_id,
                user_id=result.journal_entry.user_id,
                title=result.journal_entry.title,
                journal_type=result.journal_entry.journal_type.value,
                processing_status=result.journal_entry.processing_status.value,
                uploaded_at=result.journal_entry.uploaded_at.isoformat(),
                processed_at=result.journal_entry.processed_at.isoformat() if result.journal_entry.processed_at else None,
                word_count=result.journal_entry.metadata.word_count,
                has_interpretation=result.interpretation is not None
            )

        return ProcessingResultOut(
            result_id=result.result_id,
            success=result.success,
            processing_time_seconds=result.total_processing_time_seconds,
            error_message=result.error_message,
            warnings=result.warnings,
            journal=journal_out
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Reprocessing failed: {str(e)}")


